<?php

declare(strict_types=1);

namespace App\Filament\Resources\StudentResource\Pages;

use App\Filament\Resources\StudentResource;
use App\Filament\Resources\StudentResource\Widgets\StudentStats;
use App\Livewire\MultipleStudentWidgets;
use App\Models\GeneralSetting;
use Exception;
use Filament\Actions;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Toggle;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\ViewRecord;
use Illuminate\Support\Facades\Auth;
use Mansoor\FilamentVersionable\Page\RevisionsAction;

final class ViewStudents extends ViewRecord
{
    protected static string $resource = StudentResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\EditAction::make(),
            Actions\DeleteAction::make(),
            RevisionsAction::make(),
            // Link Student Account Action
            Actions\Action::make('linkStudentAccount')
                ->label('Link/Update Student Account')
                ->icon('heroicon-o-user-circle')
                ->color('primary')
                ->requiresConfirmation()
                ->modalHeading('Link Student Account')
                ->modalDescription('This will find and update the account associated with this student\'s email, setting the role to "student" and linking it to this student record.')
                ->action(function ($record): void {
                    if (! $record->email) {
                        Notification::make()
                            ->warning()
                            ->title('No Email Found')
                            ->body('This student does not have an email address to link to an account.')
                            ->send();

                        return;
                    }

                    $account = \App\Models\Account::where('email', $record->email)->first();

                    if (! $account) {
                        Notification::make()
                            ->warning()
                            ->title('No Account Found')
                            ->body('No account was found with the email: '.$record->email)
                            ->send();

                        return;
                    }

                    try {
                        $account->update([
                            'role' => 'student',
                            'person_id' => $record->id,
                            'person_type' => \App\Models\Student::class,
                        ]);

                        Notification::make()
                            ->success()
                            ->title('Account Linked')
                            ->body('Successfully linked account to student. Email: '.$record->email)
                            ->send();
                    } catch (Exception $e) {
                        Notification::make()
                            ->danger()
                            ->title('Error Linking Account')
                            ->body('An error occurred: '.$e->getMessage())
                            ->send();
                    }
                }),

            // Retry Class Enrollment Action
            Actions\Action::make('retryClassEnrollment')
                ->label('Retry Class Enrollment')
                ->icon('heroicon-o-arrow-path')
                ->color('success')
                ->requiresConfirmation()
                ->modalHeading('Retry Class Enrollment?')
                ->modalDescription('This will attempt to re-enroll the student in all available classes for their current subjects. Force enrollment is enabled by default to override maximum class size limits.')
                ->form([
                    Toggle::make('force_enrollment')
                        ->label('Force Enrollment')
                        ->helperText('Override maximum class size limits when enrolling')
                        ->default(true),
                    Select::make('enrollment_id')
                        ->label('Enrollment to Use')
                        ->options(fn ($record) => $record->subjectEnrolled()
                            ->select('enrollment_id')
                            ->distinct()
                            ->get()
                            ->pluck('enrollment_id', 'enrollment_id')
                            ->map(fn ($id): string => "Enrollment #{$id}")
                            ->toArray())
                        ->helperText('Select which enrollment to use for class assignments. Leave empty to use all subjects.')
                        ->searchable()
                        ->placeholder('All Subjects'),
                ])
                ->action(function (array $data, $record): void {
                    // Temporarily override the force_enroll_when_full config if needed
                    $originalConfigValue = config('enrollment.force_enroll_when_full');
                    if ($data['force_enrollment']) {
                        config(['enrollment.force_enroll_when_full' => true]);
                    }

                    try {
                        // Attempt to auto-enroll using the specified enrollment ID or null for all subjects
                        $enrollmentId = $data['enrollment_id'] ?? null;
                        $record->autoEnrollInClasses($enrollmentId);

                        Notification::make()
                            ->success()
                            ->title('Enrollment Retry Complete')
                            ->body('The system has attempted to enroll the student in all classes. Check the notification for results.')
                            ->send();
                    } catch (Exception $e) {
                        Notification::make()
                            ->danger()
                            ->title('Enrollment Retry Failed')
                            ->body('An error occurred: '.$e->getMessage())
                            ->send();
                    } finally {
                        // Restore original config value
                        if ($data['force_enrollment']) {
                            config(['enrollment.force_enroll_when_full' => $originalConfigValue]);
                        }
                    }
                }),

            Actions\Action::make('manageTuition')
                ->label('Manage Tuition')
                ->icon('heroicon-o-currency-dollar')
                ->color('warning')
                ->modalHeading('Manage Student Tuition')
                ->modalDescription('Update the tuition information for this student in the current semester')
                ->modalSubmitActionLabel('Save Tuition Information')
                ->fillForm(function ($record): array {
                    $tuition = $record->getCurrentTuitionModel();
                    $course = $record->Course;

                    if (! $tuition) {
                        return [
                            'total_lectures' => 0,
                            'total_laboratory' => 0,
                            'total_miscelaneous_fees' => $course ? $course->getMiscellaneousFee() : 3500,
                            'downpayment' => 0,
                            'discount' => 0,
                        ];
                    }

                    return [
                        'total_lectures' => $tuition->total_lectures,
                        'total_laboratory' => $tuition->total_laboratory,
                        'total_miscelaneous_fees' => $tuition->total_miscelaneous_fees,
                        'downpayment' => $tuition->downpayment,
                        'discount' => $tuition->discount,
                    ];
                })
                ->form([
                    \Filament\Forms\Components\Section::make('Tuition Fees')
                        ->schema([
                            \Filament\Forms\Components\TextInput::make('total_lectures')
                                ->label('Lecture Fees')
                                ->numeric()
                                ->prefix('₱')
                                ->default(0)
                                ->live(onBlur: true)
                                ->afterStateUpdated(function ($state, $set, $get): void {
                                    $this->calculateTotals($set, $get);
                                }),
                            \Filament\Forms\Components\TextInput::make('total_laboratory')
                                ->label('Laboratory Fees')
                                ->numeric()
                                ->prefix('₱')
                                ->default(0)
                                ->live(onBlur: true)
                                ->afterStateUpdated(function ($state, $set, $get): void {
                                    $this->calculateTotals($set, $get);
                                }),
                            \Filament\Forms\Components\TextInput::make('total_miscelaneous_fees')
                                ->label('Miscellaneous Fees')
                                ->numeric()
                                ->prefix('₱')
                                ->default(3500)
                                ->live(onBlur: true)
                                ->afterStateUpdated(function ($state, $set, $get): void {
                                    $this->calculateTotals($set, $get);
                                }),
                        ])->columns(3),

                    \Filament\Forms\Components\Section::make('Payment Information')
                        ->schema([
                            \Filament\Forms\Components\TextInput::make('downpayment')
                                ->label('Downpayment')
                                ->numeric()
                                ->prefix('₱')
                                ->default(0)
                                ->live(onBlur: true)
                                ->afterStateUpdated(function ($state, $set, $get): void {
                                    $this->calculateTotals($set, $get);
                                }),
                            \Filament\Forms\Components\TextInput::make('discount')
                                ->label('Discount (%)')
                                ->numeric()
                                ->suffix('%')
                                ->default(0)
                                ->live(onBlur: true)
                                ->afterStateUpdated(function ($state, $set, $get): void {
                                    $this->calculateTotals($set, $get);
                                }),
                        ])->columns(2),

                    \Filament\Forms\Components\Section::make('Calculated Totals')
                        ->schema([
                            \Filament\Forms\Components\TextInput::make('total_tuition')
                                ->label('Total Tuition (Lectures + Laboratory)')
                                ->prefix('₱')
                                ->disabled()
                                ->dehydrated(false),
                            \Filament\Forms\Components\TextInput::make('overall_tuition')
                                ->label('Overall Tuition (Including Misc Fees)')
                                ->prefix('₱')
                                ->disabled()
                                ->dehydrated(false),
                            \Filament\Forms\Components\TextInput::make('total_balance')
                                ->label('Total Balance')
                                ->prefix('₱')
                                ->disabled()
                                ->dehydrated(false),
                        ])->columns(3),
                ])
                ->action(function (array $data, $record): void {
                    $tuition = $record->getOrCreateCurrentTuition();
                    $settings = GeneralSetting::first();

                    // Calculate totals
                    $totalTuition = (float) $data['total_lectures'] + (float) $data['total_laboratory'];
                    $overallTuition = $totalTuition + (float) $data['total_miscelaneous_fees'];

                    // Apply discount
                    $discountAmount = $overallTuition * ((float) $data['discount'] / 100);
                    $overallTuitionAfterDiscount = $overallTuition - $discountAmount;

                    $totalBalance = $overallTuitionAfterDiscount - (float) $data['downpayment'];

                    // Calculate original lecture fee before discount
                    $discount = (float) $data['discount'];
                    $originalLecture = $discount > 0 ? (float) $data['total_lectures'] / (1 - $discount / 100) : (float) $data['total_lectures'];

                    $tuition->update([
                        'total_lectures' => (float) $data['total_lectures'],
                        'original_lectures' => $originalLecture,
                        'total_laboratory' => (float) $data['total_laboratory'],
                        'total_miscelaneous_fees' => (float) $data['total_miscelaneous_fees'],
                        'total_tuition' => $totalTuition,
                        'overall_tuition' => $overallTuitionAfterDiscount,
                        'downpayment' => (float) $data['downpayment'],
                        'discount' => $discount,
                        'total_balance' => $totalBalance,
                        'status' => $totalBalance <= 0 ? 'paid' : 'pending',
                    ]);

                    Notification::make()
                        ->title('Tuition Updated')
                        ->body('The student tuition information has been updated for the '.$settings->getSemester().' of '.$settings->getSchoolYearString())
                        ->success()
                        ->send();
                }),

            Actions\Action::make('history')
                ->label('Student History')
                ->url(fn ($record): string => StudentHistory::getUrl(['record' => $record])),
            Actions\Action::make('manageClearance')
                ->label('Manage Clearance')
                ->icon('heroicon-o-check-circle')
                ->color('success')
                ->visible(fn (): bool => (bool) GeneralSetting::first()->enable_clearance_check)
                ->form([
                    Toggle::make('is_cleared')
                        ->label('Is Cleared')
                        ->helperText('Set whether this student has cleared their requirements for the current semester')
                        ->required(),

                    \Filament\Forms\Components\DateTimePicker::make('cleared_at')
                        ->label('Cleared At')
                        ->visible(fn (\Filament\Forms\Get $get): bool => (bool) $get('is_cleared'))
                        ->default(now())
                        ->displayFormat('F j, Y g:i A')
                        ->seconds(false),

                    \Filament\Forms\Components\Textarea::make('remarks')
                        ->label('Remarks')
                        ->placeholder('Enter any notes about this clearance status')
                        ->helperText('Optional notes about the clearance status')
                        ->columnSpan(2),
                ])
                ->action(function (array $data, $record): void {
                    $user = Auth::user();
                    $clearedBy = $user ? $user->name : 'System';
                    $settings = GeneralSetting::first();

                    if ($data['is_cleared']) {
                        $success = $record->markClearanceAsCleared(
                            $clearedBy,
                            $data['remarks'] ?? null
                        );

                        if ($success) {
                            Notification::make()
                                ->title('Clearance Approved')
                                ->body('The student has been cleared for the '.$settings->getSemester().' of '.$settings->getSchoolYearString())
                                ->success()
                                ->send();
                        }
                    } else {
                        $success = $record->markClearanceAsNotCleared($data['remarks'] ?? null);

                        if ($success) {
                            Notification::make()
                                ->title('Clearance Status Updated')
                                ->body('The student is marked as not cleared for the '.$settings->getSemester().' of '.$settings->getSchoolYearString())
                                ->warning()
                                ->send();
                        }
                    }
                }),

            Actions\ForceDeleteAction::make(),
            Actions\RestoreAction::make(),
        ];
    }

    protected function getFooterWidgets(): array
    {
        return [
            StudentStats::class,
            // MultipleStudentWidgets::class,

        ];
    }

    /**
     * Calculate tuition totals and update form fields
     */
    private function calculateTotals($set, $get): void
    {
        $lectures = (float) ($get('total_lectures') ?? 0);
        $laboratory = (float) ($get('total_laboratory') ?? 0);
        $miscellaneous = (float) ($get('total_miscelaneous_fees') ?? 0);
        $downpayment = (float) ($get('downpayment') ?? 0);
        $discount = (float) ($get('discount') ?? 0);

        // Calculate total tuition (lectures + laboratory)
        $totalTuition = $lectures + $laboratory;

        // Calculate overall tuition (including miscellaneous)
        $overallTuition = $totalTuition + $miscellaneous;

        // Apply discount
        $discountAmount = $overallTuition * ($discount / 100);
        $overallTuitionAfterDiscount = $overallTuition - $discountAmount;

        // Calculate balance
        $totalBalance = $overallTuitionAfterDiscount - $downpayment;

        // Update the calculated fields
        $set('total_tuition', number_format($totalTuition, 2));
        $set('overall_tuition', number_format($overallTuitionAfterDiscount, 2));
        $set('total_balance', number_format($totalBalance, 2));
    }
}
