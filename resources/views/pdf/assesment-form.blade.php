<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Assessment Form</title>
    <script src="https://cdn.tailwindcss.com?plugins=forms,typography,aspect-ratio"></script>
    <style>
        /* Ensure Tailwind styles are properly applied */
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap');

        * {
            font-family: 'Inter', DejaVu Sans, Arial, sans-serif;
        }

        /* Force background colors to print */
        * {
            -webkit-print-color-adjust: exact !important;
            print-color-adjust: exact !important;
        }
    </style>
    <style>
        @page {
            size: landscape;
            margin: 5mm;
        }
        body {
            font-family: DejaVu Sans, Arial, sans-serif;
            line-height: 1.1;
            margin: 0;
            padding: 0;
            color: #333;
            font-size: 8pt;
        }
    </style>
</head>
<body class="bg-white mt-0">
    <div class="max-w-full mx-auto">
        <!-- Header Section -->
        <div class="mb-1 flex items-center justify-center gap-2">
            @if(isset($general_settings) && $general_settings?->school_portal_logo)
                <img src="{{ $general_settings->school_portal_logo }}" alt="College Logo" class="w-12">
            @else
                <img src="{{ asset('logo.png') }}" alt="College Logo" class="w-12">
            @endif
            <div class="text-center flex-1">
                <h1 class="font-bold text-sm">
                    {{ $general_settings?->school_portal_title ?? $general_settings?->site_name ?? 'Data Center College of the Philippines of Baguio City, Inc.' }}
                </h1>
                <p class="text-[7pt]">
                    @if(isset($general_settings) && ($general_settings?->support_phone || $general_settings?->support_email))
                        @if($general_settings?->support_phone)Tel No. {{ $general_settings->support_phone }}@endif
                        @if($general_settings?->support_phone && $general_settings?->support_email) | @endif
                        @if($general_settings?->support_email)Email: {{ $general_settings->support_email }}@endif
                    @else
                        118 Bonifacio Street, Holyghost Proper, Baguio City | Tel No. 444-5389/442-4160
                    @endif
                </p>
                <p class="text-xs font-bold">Assessment Form</p>
            </div>
        </div>

        <div class="flex gap-4">
            <!-- Left Column -->
            <div class="w-2/3">
                <!-- Student Info -->
                <div class="bg-gray-50 p-1 rounded text-[7pt]">
                    <p class="mb-0.5"><strong>Course:</strong> {{ $student->getCourse->code ?? $student->course->code }}</p>
                    <p class="mb-0.5"><strong>Full Name:</strong> {{ $student->student_name }} : <strong>{{ $student->student->id }}</strong></p>
                    <p class="mb-0.5"><strong>Semester/School Year:</strong> {{ $semester }} {{ $school_year }}</p>
                    <p class="mb-0.5"><strong>Date:</strong> {{ now()->format('m-d-Y') }}</p>
                </div>

                <!-- Subjects Table -->
                <div class="overflow-x-auto mb-2">
                    <table class="w-full text-[7pt]">
                        <thead>
                            <tr class="bg-blue-500 text-white">
                                <th class="border px-1 py-0.5 text-left">Code</th>
                                <th class="border px-1 py-0.5 text-left">Title</th>
                                <th class="border px-1 py-0.5 text-left">Units</th>
                                <th class="border px-1 py-0.5 text-left">Lec Fee</th>
                                <th class="border px-1 py-0.5 text-left">Lab Fee</th>
                            </tr>
                        </thead>
                        <tbody>
                            @php
                                $totalUnits = 0;
                                $total_lecture = 0;
                                $total_laboratory = 0;
                                $totalUnits2 = $subjects->sum('subject.units');
                            @endphp
                            @foreach ($subjects as $subject)
                                <tr class="hover:bg-gray-50">
                                    <td class="border px-1 py-0.5">{{ $subject->subject->code }}</td>
                                    <td class="border px-1 py-0.5">{{ $subject->subject->title }}</td>
                                    <td class="border px-1 py-0.5">{{ $subject->subject->units }}</td>
                                    <td class="border px-1 py-0.5">

                                        @php
                                            $lectureFee = $subject->subject->units * $subject->subject->course->lec_per_unit;
                                            if (str_contains(strtoupper($subject->subject->code), 'NSTP')) {
                                                $lectureFee *= 0.5;
                                            }
                                        @endphp
                                        {{ $lectureFee }}
                                    </td>
                                    <td class="border px-1 py-0.5">
                                        @if ($subject->subject->laboratory !== 0)
                                            {{ 1 * $subject->subject->course->lab_per_unit }}
                                        @else
                                            0
                                        @endif
                                    </td>
                                </tr>
                                @php
                                    $totalUnits2 = $subjects->sum('subject.units');
                                    $total_lecture += $lectureFee;
                                    if ($subject->subject->laboratory !== 0) {
                                        $total_laboratory += 1 * $subject->subject->course->lab_per_unit;
                                    } else {
                                        $total_laboratory += 0;
                                    }
                                @endphp
                            @endforeach
                            <tr class="font-bold bg-gray-100">
                                <td colspan="2" class="border px-1 py-0.5">Total</td>
                                <td class="border px-1 py-0.5">{{ $totalUnits2 }}</td>
                                <td class="border px-1 py-0.5">{{ number_format($total_lecture, 2) }}</td>
                                <td class="border px-1 py-0.5">{{ number_format($total_laboratory, 2) }}</td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <!-- Schedule Table -->
                <div class="overflow-x-auto">
                    <table class="w-full text-[6pt]">
                        <thead>
                            <tr class="bg-blue-500 text-white">
                                <th class="border px-1 py-0.5">Title</th>
                                <th class="border px-1 py-0.5">Mon</th>
                                <th class="border px-1 py-0.5">Tue</th>
                                <th class="border px-1 py-0.5">Wed</th>
                                <th class="border px-1 py-0.5">Thu</th>
                                <th class="border px-1 py-0.5">Fri</th>
                                <th class="border px-1 py-0.5">Sat</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach ($subjects as $subject)
                                @php
                                    // Use class_id directly instead of section field for more reliable lookup
                                    $classId = $subject->class_id;
                                    $subjectCode = $subject->subject->code;

                                    // First try: exact match by class_id (most reliable)
                                    $class = \App\Models\Classes::find($classId);

                                    // If class not found or subject codes don't match (case-insensitive), try alternative lookup
                                    if (!$class || strcasecmp($class->subject_code, $subjectCode) !== 0) {
                                        // Fallback: find by subject code (case-insensitive) and other criteria
                                        $class = \App\Models\Classes::whereRaw('LOWER(subject_code) = LOWER(?)', [$subjectCode])
                                            ->where('school_year', '2025 - 2026')
                                            ->where('semester', 1)
                                            ->first();
                                    }

                                    $daysOfWeek = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday'];
                                    $scheduleByDay = array_fill_keys($daysOfWeek, '');

                                    if ($class) {
                                        $schedules = $class->Schedule;
                                        foreach ($schedules as $schedule) {
                                            $day = strtolower($schedule->day_of_week);
                                            $room = $schedule->room->name ?? '';
                                            $section = $class->section ?? '';
                                            if (in_array($day, $daysOfWeek)) {
                                                $scheduleByDay[$day] = $schedule->start_time->format('g:i') . '-' . $schedule->end_time->format('g:i') . ' ' . $section . ' (' . $room . ')';
                                            }
                                        }
                                    }
                                @endphp
                                <tr>
                                    <td class="border px-1 py-0.5">{{ $subject->subject->title }}</td>
                                    @foreach ($daysOfWeek as $day)
                                        <td class="border px-1 py-0.5 {{ $scheduleByDay[$day] ? 'bg-blue-100' : '' }}">
                                            {{ $scheduleByDay[$day] }}
                                        </td>
                                    @endforeach
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Right Column -->
            <div class="w-1/3 border-l border-gray-300 pl-1">
                <div class="bg-gray-50 p-1 rounded">
                    <h2 class="text-xs font-bold text-gray-800 border-b border-gray-300 pb-0.5 mb-1">Breakdown of Fees</h2>

                    <!-- Tuition Fee Section -->
                    <div class="bg-white p-1 rounded shadow-sm mb-1 text-[7pt]">
                        <p class="font-bold mb-1">Tuition Fee Details</p>
                        <p class="mb-0.5">Sub-Total (tuition fee): ₱{{ number_format($tuition->original_lectures ?? $tuition->total_lectures, 2) }}</p>
                        <p class="mb-0.5">Discount: {{ $tuition->discount }}%</p>
                        <p class="border-t border-gray-200 pt-1 mt-1 font-bold">
                            Total Tuition Fee: ₱{{ number_format($tuition->total_lectures, 2) }}
                        </p>
                    </div>

                    <!-- Additional Fees Section -->
                    <div class="bg-white p-1 rounded shadow-sm mb-1 text-[7pt]">
                        <p class="font-bold mb-1">Additional Fees</p>
                        <p class="mb-0.5">Laboratory Fee: ₱{{ number_format($tuition->total_laboratory, 2) }}</p>
                        <p class="mb-0.5">Miscellaneous Fee: ₱{{ number_format($tuition->total_miscelaneous_fees, 2) }}</p>
                    </div>

                    <!-- Payment Summary -->
                    <div class="bg-blue-50 p-1 rounded border border-blue-200 mb-1 text-[7pt]">
                        <p class="font-bold mb-1">Payment Summary</p>
                        <p class="mb-0.5">Total Amount: ₱{{ number_format($tuition->overall_tuition, 2) }}</p>
                        <p class="mb-0.5">Downpayment: ₱{{ number_format($tuition->downpayment, 2) }}</p>
                        <p class="text-sm font-bold text-gray-800">
                            Balance: ₱{{ number_format($tuition->total_balance, 2) }}
                        </p>
                    </div>

                    <!-- Signatures Section -->
                    <div class="space-y-6 mt-7">
                        <div>
                            <div class="border-b border-black w-48"></div>
                            <p class="text-[8pt] mt-0.5">Assessed By</p>
                        </div>

                        <div>
                            <div class="border-b border-black w-48"></div>
                            <p class="text-[8pt] mt-0.5">Student Signature</p>
                        </div>

                        <div>
                            <div class="border-b border-black w-48"></div>
                            <p class="text-[8pt] mt-0.5">Registrar</p>
                        </div>

                        <div>
                            <div class="border-b border-black w-48"></div>
                            <p class="text-[8pt] mt-0.5">Cashier</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
