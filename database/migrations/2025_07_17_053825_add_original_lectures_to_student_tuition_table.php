<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('student_tuition', function (Blueprint $table) {
            $table->float('original_lectures')->nullable()->after('total_lectures')
                ->comment('Original lecture fee amount before discount application');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('student_tuition', function (Blueprint $table) {
            $table->dropColumn('original_lectures');
        });
    }
};
